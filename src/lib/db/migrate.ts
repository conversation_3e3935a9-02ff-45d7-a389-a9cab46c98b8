#!/usr/bin/env bun
// PostgreSQL migration script

import { postgresDb } from './client';

async function runMigrations() {
  try {
    console.log('🚀 Starting PostgreSQL migrations...');

    // Initialize database connection
    await postgresDb.initialize();

    // Check database health
    const health = await postgresDb.checkHealth();
    if (!health.isHealthy) {
      console.error('❌ Database health check failed:', health.errors);
      process.exit(1);
    }

    console.log('✅ Database connection established');
    console.log('✅ All tables created successfully');
    console.log('✅ Indexes created for optimal performance');
    console.log('🎉 Migration completed successfully!');

    // Close database connection
    await postgresDb.close();

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migrations if this script is executed directly
if (import.meta.main) {
  runMigrations();
}

export { runMigrations };
