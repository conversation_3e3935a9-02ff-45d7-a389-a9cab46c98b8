import { Pool, PoolClient } from 'pg';
import { dbConfig } from './config';

export class PostgreSQLDatabaseManager {
  private pool: Pool | null = null;
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Create PostgreSQL connection pool
      this.pool = new Pool({
        host: dbConfig.postgres.host,
        port: dbConfig.postgres.port,
        database: dbConfig.postgres.database,
        user: dbConfig.postgres.user,
        password: dbConfig.postgres.password,
        ssl: dbConfig.postgres.ssl,
        min: dbConfig.pool.min,
        max: dbConfig.pool.max,
        idleTimeoutMillis: dbConfig.pool.idleTimeoutMillis,
        connectionTimeoutMillis: dbConfig.pool.connectionTimeoutMillis,
      });

      // Test the connection
      await this.checkHealth();

      // Run migrations if needed
      await this.runMigrations();

      this.isInitialized = true;
      console.log('PostgreSQL database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize PostgreSQL database:', error);
      throw error;
    }
  }

  private async runMigrations(): Promise<void> {
    if (!this.pool) throw new Error('Database not initialized');

    try {
      // Create migrations table if it doesn't exist
      await this.createMigrationsTable();

      // For now, we'll create tables manually since we don't have migration files yet
      await this.createTablesIfNotExists();
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }

  private async createMigrationsTable(): Promise<void> {
    if (!this.pool) throw new Error('Database not initialized');

    const sql = `
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;

    await this.pool.query(sql);
  }

  private async createTablesIfNotExists(): Promise<void> {
    if (!this.pool) throw new Error('Database not initialized');

    const sql = `
      -- Characters table (base characters in the game)
      CREATE TABLE IF NOT EXISTS characters (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        name_jp VARCHAR(255),
        name_en VARCHAR(255),
        name_zh VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- Skills table
      CREATE TABLE IF NOT EXISTS skills (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100) NOT NULL,
        description TEXT,
        icon VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- Swimsuits table
      CREATE TABLE IF NOT EXISTS swimsuits (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        character_id VARCHAR(255) NOT NULL,
        rarity VARCHAR(10) NOT NULL CHECK (rarity IN ('SSR', 'SR', 'R')),
        pow INTEGER NOT NULL,
        tec INTEGER NOT NULL,
        stm INTEGER NOT NULL,
        apl INTEGER NOT NULL,
        release_date DATE NOT NULL,
        reappear_date DATE,
        image VARCHAR(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );

      -- Girls table (User's collection)
      CREATE TABLE IF NOT EXISTS girls (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(10) NOT NULL CHECK (type IN ('pow', 'tec', 'stm')),
        level INTEGER NOT NULL,
        pow INTEGER NOT NULL,
        tec INTEGER NOT NULL,
        stm INTEGER NOT NULL,
        apl INTEGER NOT NULL,
        max_pow INTEGER NOT NULL,
        max_tec INTEGER NOT NULL,
        max_stm INTEGER NOT NULL,
        max_apl INTEGER NOT NULL,
        birthday DATE NOT NULL,
        swimsuit_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (swimsuit_id) REFERENCES swimsuits(id)
      );

      -- Accessories table
      CREATE TABLE IF NOT EXISTS accessories (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(20) NOT NULL CHECK (type IN ('head', 'face', 'hand')),
        skill_id VARCHAR(255) NOT NULL,
        pow INTEGER,
        tec INTEGER,
        stm INTEGER,
        apl INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE
      );

      -- Swimsuit Skills junction table
      CREATE TABLE IF NOT EXISTS swimsuit_skills (
        swimsuit_id VARCHAR(255) NOT NULL,
        skill_id VARCHAR(255) NOT NULL,
        position INTEGER NOT NULL,
        PRIMARY KEY (swimsuit_id, skill_id),
        FOREIGN KEY (swimsuit_id) REFERENCES swimsuits(id) ON DELETE CASCADE,
        FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE
      );

      -- Girl Accessories junction table
      CREATE TABLE IF NOT EXISTS girl_accessories (
        girl_id VARCHAR(255) NOT NULL,
        accessory_id VARCHAR(255) NOT NULL,
        equipped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (girl_id, accessory_id),
        FOREIGN KEY (girl_id) REFERENCES girls(id) ON DELETE CASCADE,
        FOREIGN KEY (accessory_id) REFERENCES accessories(id) ON DELETE CASCADE
      );

      -- Venus Boards table
      CREATE TABLE IF NOT EXISTS venus_boards (
        id SERIAL PRIMARY KEY,
        girl_id VARCHAR(255) NOT NULL,
        pow INTEGER NOT NULL,
        tec INTEGER NOT NULL,
        stm INTEGER NOT NULL,
        apl INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (girl_id) REFERENCES girls(id) ON DELETE CASCADE
      );

      -- User Settings table
      CREATE TABLE IF NOT EXISTS user_settings (
        key VARCHAR(255) PRIMARY KEY,
        value TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- Create indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_swimsuits_character_id ON swimsuits(character_id);
      CREATE INDEX IF NOT EXISTS idx_girls_swimsuit_id ON girls(swimsuit_id);
      CREATE INDEX IF NOT EXISTS idx_accessories_skill_id ON accessories(skill_id);
      CREATE INDEX IF NOT EXISTS idx_swimsuit_skills_swimsuit_id ON swimsuit_skills(swimsuit_id);
      CREATE INDEX IF NOT EXISTS idx_swimsuit_skills_skill_id ON swimsuit_skills(skill_id);
      CREATE INDEX IF NOT EXISTS idx_girl_accessories_girl_id ON girl_accessories(girl_id);
      CREATE INDEX IF NOT EXISTS idx_girl_accessories_accessory_id ON girl_accessories(accessory_id);
      CREATE INDEX IF NOT EXISTS idx_venus_boards_girl_id ON venus_boards(girl_id);
    `;

    await this.pool.query(sql);
  }

  async query(text: string, params?: any[]): Promise<any> {
    if (!this.pool) throw new Error('Database not initialized');

    if (dbConfig.development.logQueries) {
      console.log('Executing query:', text, params);
    }

    return await this.pool.query(text, params);
  }

  async getClient(): Promise<PoolClient> {
    if (!this.pool) throw new Error('Database not initialized');
    return await this.pool.connect();
  }

  getPool(): Pool {
    if (!this.pool) throw new Error('Database not initialized');
    return this.pool;
  }

  async saveChanges(): Promise<void> {
    // PostgreSQL automatically commits transactions
    // This method is kept for compatibility with the existing API
  }

  async checkHealth(): Promise<{ isHealthy: boolean; errors: string[] }> {
    const errors: string[] = [];

    if (!this.isInitialized) {
      errors.push('Database not initialized');
      return { isHealthy: false, errors };
    }

    try {
      // Test connection
      const result = await this.query('SELECT 1 as test');
      if (!result.rows || result.rows.length === 0) {
        errors.push('Database connection test failed');
      }

      // Check if all tables exist
      const tables = ['characters', 'skills', 'swimsuits', 'girls', 'accessories', 'swimsuit_skills', 'girl_accessories', 'venus_boards', 'user_settings'];
      const tableCheckResult = await this.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
      `);

      const existingTables = tableCheckResult.rows.map((row: any) => row.table_name);

      tables.forEach(table => {
        if (!existingTables.includes(table)) {
          errors.push(`Missing table: ${table}`);
        }
      });

    } catch (error: any) {
      errors.push(`Database check failed: ${error.message}`);
    }

    return {
      isHealthy: errors.length === 0,
      errors
    };
  }

  async close(): Promise<void> {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
      this.isInitialized = false;
    }
  }
}

// Export singleton instance
export const postgresDb = new PostgreSQLDatabaseManager();
