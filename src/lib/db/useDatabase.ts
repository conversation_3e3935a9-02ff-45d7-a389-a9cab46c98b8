import { useEffect, useCallback } from 'react';
import { useAppStore } from '@/store';
import { postgresDb } from './client';
import { postgresService } from './service';
import { seedDatabase, clearDatabase, resetDatabase } from './seeder';
import type { Girl, Swimsuit, Skill, Accessory } from './types';

export function usePostgreSQLDatabase() {
  const store = useAppStore();

  // Initialize database on first load
  useEffect(() => {
    const initializeDatabase = async () => {
      try {
        await postgresDb.initialize();

        // Load data from database into store
        await loadDataFromDatabase();

        // Load user settings
        await loadUserSettings();

        console.log('PostgreSQL database integration completed successfully');
      } catch (error) {
        console.error('Failed to initialize PostgreSQL database:', error);
      }
    };

    initializeDatabase();
  }, []);

  const loadDataFromDatabase = useCallback(async () => {
    try {
      // Load skills
      const dbSkills = await postgresService.getSkills();
      const skills = dbSkills.map(skill => convertDbSkillToSkill(skill));
      store.setSkills(skills);

      // Load swimsuits
      const dbSwimsuits = await postgresService.getSwimsuits();
      const swimsuits = dbSwimsuits.map(swimsuit => convertDbSwimsuitToSwimsuit(swimsuit));
      store.setSwimsuits(swimsuits);

      // Load girls
      const dbGirls = await postgresService.getGirls();
      const girls = await Promise.all(dbGirls.map(girl => convertDbGirlToGirl(girl)));
      store.setGirls(girls);

      // Load accessories
      const dbAccessories = await postgresService.getAccessories();
      const accessories = dbAccessories.map(accessory => convertDbAccessoryToAccessory(accessory));
      store.setAccessories(accessories);

      console.log('Data loaded from PostgreSQL database successfully');
    } catch (error) {
      console.error('Failed to load data from PostgreSQL database:', error);
    }
  }, [store]);

  const loadUserSettings = useCallback(async () => {
    try {
      const theme = await postgresService.getSetting('theme');
      const language = await postgresService.getSetting('language');
      const sidebarCollapsed = await postgresService.getSetting('sidebarCollapsed');

      if (theme) store.setTheme(theme as any);
      if (language) store.setCurrentLanguage(language);
      if (sidebarCollapsed) store.setSidebarCollapsed(sidebarCollapsed === 'true');
    } catch (error) {
      console.error('Failed to load user settings:', error);
    }
  }, [store]);

  const saveUserSettings = useCallback(async () => {
    try {
      await postgresService.setSetting('theme', store.theme);
      await postgresService.setSetting('language', store.currentLanguage);
      await postgresService.setSetting('sidebarCollapsed', store.sidebarCollapsed.toString());
    } catch (error) {
      console.error('Failed to save user settings:', error);
    }
  }, [store.theme, store.currentLanguage, store.sidebarCollapsed]);

  // Save settings when they change
  useEffect(() => {
    saveUserSettings();
  }, [saveUserSettings]);

  const saveVenusBoard = useCallback(async (girlId: string) => {
    try {
      const venusBoard = store.venusBoard;
      await postgresService.createVenusBoard({
        girlId,
        pow: venusBoard.pow,
        tec: venusBoard.tec,
        stm: venusBoard.stm,
        apl: venusBoard.apl,
      });
    } catch (error) {
      console.error('Failed to save Venus Board:', error);
    }
  }, [store.venusBoard]);

  const loadVenusBoard = useCallback(async (girlId: string) => {
    try {
      const venusBoards = await postgresService.getVenusBoardsByGirlId(girlId);
      if (venusBoards.length > 0) {
        const latestBoard = venusBoards[venusBoards.length - 1];
        store.updateVenusBoard({
          pow: latestBoard.pow,
          tec: latestBoard.tec,
          stm: latestBoard.stm,
          apl: latestBoard.apl,
        });
      }
    } catch (error) {
      console.error('Failed to load Venus Board:', error);
    }
  }, [store]);

  const createSwimsuit = useCallback(async (swimsuit: any) => {
    try {
      const dbSwimsuit = {
        id: swimsuit.id,
        name: swimsuit.name,
        characterId: swimsuit.character?.id || 'unknown',
        rarity: swimsuit.rarity as 'SSR' | 'SR' | 'R',
        pow: swimsuit.stats.pow,
        tec: swimsuit.stats.tec,
        stm: swimsuit.stats.stm,
        apl: swimsuit.stats.apl,
        releaseDate: new Date(swimsuit.releaseDate),
        reappearDate: swimsuit.reappearDate ? new Date(swimsuit.reappearDate) : undefined,
        image: swimsuit.image,
      };

      await postgresService.createSwimsuit(dbSwimsuit);

      // Add skills to swimsuit
      if (swimsuit.skills && swimsuit.skills.length > 0) {
        for (let i = 0; i < swimsuit.skills.length; i++) {
          await postgresService.addSkillToSwimsuit(swimsuit.id, swimsuit.skills[i].id, i);
        }
      }

      // Reload data
      await loadDataFromDatabase();
    } catch (error) {
      console.error('Failed to create swimsuit:', error);
      throw error;
    }
  }, [loadDataFromDatabase]);

  const createGirl = useCallback(async (girl: any) => {
    try {
      const dbGirl = {
        id: girl.id,
        name: girl.name,
        type: girl.type as 'pow' | 'tec' | 'stm',
        level: girl.level,
        pow: girl.stats.pow,
        tec: girl.stats.tec,
        stm: girl.stats.stm,
        apl: girl.stats.apl,
        maxPow: girl.maxStats.pow,
        maxTec: girl.maxStats.tec,
        maxStm: girl.maxStats.stm,
        maxApl: girl.maxStats.apl,
        birthday: new Date(girl.birthday),
        swimsuitId: girl.swimsuit?.id,
      };

      await postgresService.createGirl(dbGirl);

      // Reload data
      await loadDataFromDatabase();
    } catch (error) {
      console.error('Failed to create girl:', error);
      throw error;
    }
  }, [loadDataFromDatabase]);

  const createSkill = useCallback(async (skill: any) => {
    try {
      const dbSkill = {
        id: skill.id,
        name: skill.name,
        type: skill.type,
        description: skill.description || '',
        icon: skill.icon || '',
      };

      await postgresService.createSkill(dbSkill);

      // Reload data
      await loadDataFromDatabase();
    } catch (error) {
      console.error('Failed to create skill:', error);
      throw error;
    }
  }, [loadDataFromDatabase]);

  // Conversion functions
  const convertDbSkillToSkill = (dbSkill: Skill): any => ({
    id: dbSkill.id,
    name: dbSkill.name,
    type: dbSkill.type,
    description: dbSkill.description || '',
    icon: dbSkill.icon || '',
    created_at: dbSkill.createdAt,
    updated_at: dbSkill.updatedAt,
  });

  const convertDbSwimsuitToSwimsuit = (dbSwimsuit: Swimsuit): any => ({
    id: dbSwimsuit.id,
    name: dbSwimsuit.name,
    character: { id: dbSwimsuit.characterId },
    rarity: dbSwimsuit.rarity,
    stats: {
      pow: dbSwimsuit.pow,
      tec: dbSwimsuit.tec,
      stm: dbSwimsuit.stm,
      apl: dbSwimsuit.apl,
    },
    releaseDate: dbSwimsuit.releaseDate,
    reappearDate: dbSwimsuit.reappearDate,
    image: dbSwimsuit.image,
    skills: [], // Will be populated by separate query if needed
    created_at: dbSwimsuit.createdAt,
    updated_at: dbSwimsuit.updatedAt,
  });

  const convertDbGirlToGirl = async (dbGirl: Girl): Promise<any> => {
    let swimsuit = null;
    if (dbGirl.swimsuitId) {
      const dbSwimsuit = await postgresService.getSwimsuitById(dbGirl.swimsuitId);
      if (dbSwimsuit) {
        swimsuit = convertDbSwimsuitToSwimsuit(dbSwimsuit);
      }
    }

    return {
      id: dbGirl.id,
      name: dbGirl.name,
      type: dbGirl.type,
      level: dbGirl.level,
      stats: {
        pow: dbGirl.pow,
        tec: dbGirl.tec,
        stm: dbGirl.stm,
        apl: dbGirl.apl,
      },
      maxStats: {
        pow: dbGirl.maxPow,
        tec: dbGirl.maxTec,
        stm: dbGirl.maxStm,
        apl: dbGirl.maxApl,
      },
      birthday: dbGirl.birthday,
      swimsuit,
      accessories: [], // Will be populated by separate query if needed
      created_at: dbGirl.createdAt,
      updated_at: dbGirl.updatedAt,
    };
  };

  const convertDbAccessoryToAccessory = (dbAccessory: Accessory): any => ({
    id: dbAccessory.id,
    name: dbAccessory.name,
    type: dbAccessory.type,
    skill: { id: dbAccessory.skillId },
    stats: {
      pow: dbAccessory.pow || 0,
      tec: dbAccessory.tec || 0,
      stm: dbAccessory.stm || 0,
      apl: dbAccessory.apl || 0,
    },
    created_at: dbAccessory.createdAt,
    updated_at: dbAccessory.updatedAt,
  });

  const populateWithSampleData = useCallback(async () => {
    try {
      console.log('🌱 Populating with sample data...');
      await seedDatabase();
      // Reload data after seeding
      await loadDataFromDatabase();
      console.log('✅ Sample data population completed!');
    } catch (error) {
      console.error('❌ Failed to populate sample data:', error);
      throw error;
    }
  }, [loadDataFromDatabase]);

  const populateComprehensiveData = useCallback(async () => {
    try {
      console.log('🌱 Populating comprehensive database...');
      await seedDatabase();
      // Reload data after seeding
      await loadDataFromDatabase();
      console.log('✅ Comprehensive data population completed!');
    } catch (error) {
      console.error('❌ Failed to populate comprehensive data:', error);
      throw error;
    }
  }, [loadDataFromDatabase]);

  const resetDatabaseFunc = useCallback(async () => {
    try {
      console.log('🔄 Resetting database...');
      await resetDatabase();
      // Reload data after reset
      await loadDataFromDatabase();
      console.log('✅ Database reset completed!');
    } catch (error) {
      console.error('❌ Failed to reset database:', error);
      throw error;
    }
  }, [loadDataFromDatabase]);

  return {
    loadDataFromDatabase,
    loadUserSettings,
    saveUserSettings,
    saveVenusBoard,
    loadVenusBoard,
    createSwimsuit,
    createGirl,
    createSkill,
    populateWithSampleData,
    populateComprehensiveData,
    resetDatabase: resetDatabaseFunc,
    databaseManager: postgresDb, // For compatibility
  };
}
