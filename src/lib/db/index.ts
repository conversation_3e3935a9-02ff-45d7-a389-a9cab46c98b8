// Main database exports for PostgreSQL integration

// Core database components
export { postgresDb, PostgreSQLDatabaseManager } from './client';
export { postgresService, PostgreSQLService } from './service';
export { usePostgreSQLDatabase } from './useDatabase';

// Type exports
export * from './types';

// Configuration
export { dbConfig } from './config';

// Convenience re-exports for common operations
import { postgresDb } from './client';
import { postgresService } from './service';

export const db = postgresDb;
export const dbService = postgresService;

// Database initialization helper
export async function initializeDatabase() {
  await db.initialize();
  return db;
}

// Health check helper
export async function checkDatabaseHealth() {
  return await db.checkHealth();
}

// Migration helper
export async function runMigrationIfNeeded() {
  const { runMigrations } = await import('./migrate');
  try {
    await runMigrations();
    console.log('✅ Database migration completed successfully');
    return true;
  } catch (error) {
    console.error('❌ Database migration failed:', error);
    console.log('Please run: bun run db:migrate');
    return false;
  }
}
