// Database configuration for PostgreSQL
export const dbConfig = {
  // PostgreSQL connection configuration
  postgres: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'doaxvv_handbook',
    user: process.env.DB_USER || 'doaxvv_user',
    password: process.env.DB_PASSWORD || 'doaxvv_password',
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  },

  // Connection pool settings
  pool: {
    min: 2,
    max: 10,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  },

  // Database settings
  database: {
    // Enable foreign key constraints
    foreignKeys: true,
    // Connection timeout in milliseconds
    timeout: 30000,
    // Query timeout in milliseconds
    queryTimeout: 10000,
  },

  // Migration settings
  migrations: {
    // Directory for migration files
    directory: './src/lib/db/migrations',
    // Table name for migration tracking
    tableName: 'migrations',
  },

  // Development settings
  development: {
    // Enable query logging in development
    logQueries: process.env.NODE_ENV === 'development',
    // Enable detailed error logging
    logErrors: true,
  },
};

export default dbConfig;
