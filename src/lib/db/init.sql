-- PostgreSQL initialization script for DOAXVV Handbook
-- This script creates the database schema and initial data

-- Enable UUID extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Characters table (base characters in the game)
CREATE TABLE IF NOT EXISTS characters (
  id VARCHAR(255) PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  name_jp VARCHA<PERSON>(255),
  name_en VARCHAR(255),
  name_zh VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Skills table
CREATE TABLE IF NOT EXISTS skills (
  id VARCHAR(255) PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(255) NOT NULL,
  type VARCHAR(100) NOT NULL,
  description TEXT,
  icon VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Swimsuits table
CREATE TABLE IF NOT EXISTS swimsuits (
  id VARCHAR(255) PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  character_id VARCHAR(255) NOT NULL,
  rarity VARCHAR(10) NOT NULL CHECK (rarity IN ('SSR', 'SR', 'R')),
  pow INTEGER NOT NULL,
  tec INTEGER NOT NULL,
  stm INTEGER NOT NULL,
  apl INTEGER NOT NULL,
  release_date DATE NOT NULL,
  reappear_date DATE,
  image VARCHAR(500),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
);

-- Girls table (User's collection)
CREATE TABLE IF NOT EXISTS girls (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(10) NOT NULL CHECK (type IN ('pow', 'tec', 'stm')),
  level INTEGER NOT NULL,
  pow INTEGER NOT NULL,
  tec INTEGER NOT NULL,
  stm INTEGER NOT NULL,
  apl INTEGER NOT NULL,
  max_pow INTEGER NOT NULL,
  max_tec INTEGER NOT NULL,
  max_stm INTEGER NOT NULL,
  max_apl INTEGER NOT NULL,
  birthday DATE NOT NULL,
  swimsuit_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (swimsuit_id) REFERENCES swimsuits(id)
);

-- Accessories table
CREATE TABLE IF NOT EXISTS accessories (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(20) NOT NULL CHECK (type IN ('head', 'face', 'hand')),
  skill_id VARCHAR(255) NOT NULL,
  pow INTEGER,
  tec INTEGER,
  stm INTEGER,
  apl INTEGER,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE
);

-- Swimsuit Skills junction table
CREATE TABLE IF NOT EXISTS swimsuit_skills (
  swimsuit_id VARCHAR(255) NOT NULL,
  skill_id VARCHAR(255) NOT NULL,
  position INTEGER NOT NULL,
  PRIMARY KEY (swimsuit_id, skill_id),
  FOREIGN KEY (swimsuit_id) REFERENCES swimsuits(id) ON DELETE CASCADE,
  FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE
);

-- Girl Accessories junction table
CREATE TABLE IF NOT EXISTS girl_accessories (
  girl_id VARCHAR(255) NOT NULL,
  accessory_id VARCHAR(255) NOT NULL,
  equipped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (girl_id, accessory_id),
  FOREIGN KEY (girl_id) REFERENCES girls(id) ON DELETE CASCADE,
  FOREIGN KEY (accessory_id) REFERENCES accessories(id) ON DELETE CASCADE
);

-- Venus Boards table
CREATE TABLE IF NOT EXISTS venus_boards (
  id SERIAL PRIMARY KEY,
  girl_id VARCHAR(255) NOT NULL,
  pow INTEGER NOT NULL,
  tec INTEGER NOT NULL,
  stm INTEGER NOT NULL,
  apl INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (girl_id) REFERENCES girls(id) ON DELETE CASCADE
);

-- User Settings table
CREATE TABLE IF NOT EXISTS user_settings (
  key VARCHAR(255) PRIMARY KEY,
  value TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Migrations table for tracking schema changes
CREATE TABLE IF NOT EXISTS migrations (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_swimsuits_character_id ON swimsuits(character_id);
CREATE INDEX IF NOT EXISTS idx_girls_swimsuit_id ON girls(swimsuit_id);
CREATE INDEX IF NOT EXISTS idx_accessories_skill_id ON accessories(skill_id);
CREATE INDEX IF NOT EXISTS idx_swimsuit_skills_swimsuit_id ON swimsuit_skills(swimsuit_id);
CREATE INDEX IF NOT EXISTS idx_swimsuit_skills_skill_id ON swimsuit_skills(skill_id);
CREATE INDEX IF NOT EXISTS idx_girl_accessories_girl_id ON girl_accessories(girl_id);
CREATE INDEX IF NOT EXISTS idx_girl_accessories_accessory_id ON girl_accessories(accessory_id);
CREATE INDEX IF NOT EXISTS idx_venus_boards_girl_id ON venus_boards(girl_id);

-- Insert initial migration record
INSERT INTO migrations (name) VALUES ('001_initial_schema') ON CONFLICT (name) DO NOTHING;

-- Insert some sample data for testing (optional)
-- You can remove this section if you don't want sample data

-- Sample characters
INSERT INTO characters (id, name, name_jp, name_en) VALUES 
  ('kasumi', 'Kasumi', 'かすみ', 'Kasumi'),
  ('ayane', 'Ayane', 'あやね', 'Ayane'),
  ('hitomi', 'Hitomi', 'ひとみ', 'Hitomi')
ON CONFLICT (id) DO NOTHING;

-- Sample skills
INSERT INTO skills (id, name, type, description) VALUES 
  ('power_boost', 'Power Boost', 'offensive', 'Increases power stats'),
  ('tech_boost', 'Tech Boost', 'offensive', 'Increases technique stats'),
  ('stamina_boost', 'Stamina Boost', 'defensive', 'Increases stamina stats')
ON CONFLICT (id) DO NOTHING;

-- Sample swimsuits
INSERT INTO swimsuits (id, name, character_id, rarity, pow, tec, stm, apl, release_date) VALUES 
  ('kasumi_ssr_1', 'Venus Kasumi', 'kasumi', 'SSR', 1000, 800, 900, 850, '2024-01-01'),
  ('ayane_sr_1', 'Ninja Ayane', 'ayane', 'SR', 800, 900, 700, 750, '2024-01-15')
ON CONFLICT (id) DO NOTHING;

-- Sample user settings
INSERT INTO user_settings (key, value) VALUES 
  ('theme', 'dark'),
  ('language', 'en'),
  ('sidebarCollapsed', 'false')
ON CONFLICT (key) DO UPDATE SET value = EXCLUDED.value;
