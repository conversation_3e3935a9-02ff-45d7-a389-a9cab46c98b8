import { postgresDb } from './client';
import {
  Character,
  Skill,
  <PERSON>wi<PERSON><PERSON>,
  <PERSON>,
  Accessory,
  VenusBoard,
  UserSetting,
  NewCharacter,
  NewSkill,
  NewSwimsuit,
  NewGirl,
  NewAccessory,
  NewVenusBoard,
  NewUserSetting,
  SwimsuitSkill,
  GirlAccessory,
  SwimsuitWithSkills,
  GirlWithDetails,
  CharacterWithSwimsuits,
} from './types';

export class PostgreSQLService {
  private get db() {
    return postgresDb;
  }

  // Characters CRUD
  async createCharacter(character: <PERSON><PERSON>hara<PERSON>): Promise<Character> {
    const result = await this.db.query(
      `INSERT INTO characters (id, name, name_jp, name_en, name_zh, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING *`,
      [character.id, character.name, character.nameJp, character.nameEn, character.nameZh]
    );
    return this.mapCharacterRow(result.rows[0]);
  }

  async getCharacters(): Promise<Character[]> {
    const result = await this.db.query('SELECT * FROM characters ORDER BY name');
    return result.rows.map(row => this.mapCharacterRow(row));
  }

  async getCharacterById(id: string): Promise<Character | undefined> {
    const result = await this.db.query('SELECT * FROM characters WHERE id = $1', [id]);
    return result.rows[0] ? this.mapCharacterRow(result.rows[0]) : undefined;
  }

  async updateCharacter(id: string, updates: Partial<NewCharacter>): Promise<Character> {
    const setClause = [];
    const values = [];
    let paramIndex = 1;

    if (updates.name !== undefined) {
      setClause.push(`name = $${paramIndex++}`);
      values.push(updates.name);
    }
    if (updates.nameJp !== undefined) {
      setClause.push(`name_jp = $${paramIndex++}`);
      values.push(updates.nameJp);
    }
    if (updates.nameEn !== undefined) {
      setClause.push(`name_en = $${paramIndex++}`);
      values.push(updates.nameEn);
    }
    if (updates.nameZh !== undefined) {
      setClause.push(`name_zh = $${paramIndex++}`);
      values.push(updates.nameZh);
    }

    setClause.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const result = await this.db.query(
      `UPDATE characters SET ${setClause.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
      values
    );
    return this.mapCharacterRow(result.rows[0]);
  }

  async deleteCharacter(id: string): Promise<void> {
    await this.db.query('DELETE FROM characters WHERE id = $1', [id]);
  }

  // Skills CRUD
  async createSkill(skill: NewSkill): Promise<Skill> {
    const result = await this.db.query(
      `INSERT INTO skills (id, name, type, description, icon, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING *`,
      [skill.id, skill.name, skill.type, skill.description, skill.icon]
    );
    return this.mapSkillRow(result.rows[0]);
  }

  async getSkills(): Promise<Skill[]> {
    const result = await this.db.query('SELECT * FROM skills ORDER BY name');
    return result.rows.map(row => this.mapSkillRow(row));
  }

  async getSkillById(id: string): Promise<Skill | undefined> {
    const result = await this.db.query('SELECT * FROM skills WHERE id = $1', [id]);
    return result.rows[0] ? this.mapSkillRow(result.rows[0]) : undefined;
  }

  async updateSkill(id: string, updates: Partial<NewSkill>): Promise<Skill> {
    const setClause = [];
    const values = [];
    let paramIndex = 1;

    if (updates.name !== undefined) {
      setClause.push(`name = $${paramIndex++}`);
      values.push(updates.name);
    }
    if (updates.type !== undefined) {
      setClause.push(`type = $${paramIndex++}`);
      values.push(updates.type);
    }
    if (updates.description !== undefined) {
      setClause.push(`description = $${paramIndex++}`);
      values.push(updates.description);
    }
    if (updates.icon !== undefined) {
      setClause.push(`icon = $${paramIndex++}`);
      values.push(updates.icon);
    }

    setClause.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const result = await this.db.query(
      `UPDATE skills SET ${setClause.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
      values
    );
    return this.mapSkillRow(result.rows[0]);
  }

  async deleteSkill(id: string): Promise<void> {
    await this.db.query('DELETE FROM skills WHERE id = $1', [id]);
  }

  // Swimsuits CRUD
  async createSwimsuit(swimsuit: NewSwimsuit): Promise<Swimsuit> {
    const result = await this.db.query(
      `INSERT INTO swimsuits (id, name, character_id, rarity, pow, tec, stm, apl, release_date, reappear_date, image, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING *`,
      [swimsuit.id, swimsuit.name, swimsuit.characterId, swimsuit.rarity, swimsuit.pow, swimsuit.tec, swimsuit.stm, swimsuit.apl, swimsuit.releaseDate, swimsuit.reappearDate, swimsuit.image]
    );
    return this.mapSwimsuitRow(result.rows[0]);
  }

  async getSwimsuits(): Promise<Swimsuit[]> {
    const result = await this.db.query('SELECT * FROM swimsuits ORDER BY release_date DESC');
    return result.rows.map(row => this.mapSwimsuitRow(row));
  }

  async getSwimsuitById(id: string): Promise<Swimsuit | undefined> {
    const result = await this.db.query('SELECT * FROM swimsuits WHERE id = $1', [id]);
    return result.rows[0] ? this.mapSwimsuitRow(result.rows[0]) : undefined;
  }

  async updateSwimsuit(id: string, updates: Partial<NewSwimsuit>): Promise<Swimsuit> {
    const setClause = [];
    const values = [];
    let paramIndex = 1;

    if (updates.name !== undefined) {
      setClause.push(`name = $${paramIndex++}`);
      values.push(updates.name);
    }
    if (updates.characterId !== undefined) {
      setClause.push(`character_id = $${paramIndex++}`);
      values.push(updates.characterId);
    }
    if (updates.rarity !== undefined) {
      setClause.push(`rarity = $${paramIndex++}`);
      values.push(updates.rarity);
    }
    if (updates.pow !== undefined) {
      setClause.push(`pow = $${paramIndex++}`);
      values.push(updates.pow);
    }
    if (updates.tec !== undefined) {
      setClause.push(`tec = $${paramIndex++}`);
      values.push(updates.tec);
    }
    if (updates.stm !== undefined) {
      setClause.push(`stm = $${paramIndex++}`);
      values.push(updates.stm);
    }
    if (updates.apl !== undefined) {
      setClause.push(`apl = $${paramIndex++}`);
      values.push(updates.apl);
    }
    if (updates.releaseDate !== undefined) {
      setClause.push(`release_date = $${paramIndex++}`);
      values.push(updates.releaseDate);
    }
    if (updates.reappearDate !== undefined) {
      setClause.push(`reappear_date = $${paramIndex++}`);
      values.push(updates.reappearDate);
    }
    if (updates.image !== undefined) {
      setClause.push(`image = $${paramIndex++}`);
      values.push(updates.image);
    }

    setClause.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const result = await this.db.query(
      `UPDATE swimsuits SET ${setClause.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
      values
    );
    return this.mapSwimsuitRow(result.rows[0]);
  }

  async deleteSwimsuit(id: string): Promise<void> {
    await this.db.query('DELETE FROM swimsuits WHERE id = $1', [id]);
  }

  // Girls CRUD
  async createGirl(girl: NewGirl): Promise<Girl> {
    const result = await this.db.query(
      `INSERT INTO girls (id, name, type, level, pow, tec, stm, apl, max_pow, max_tec, max_stm, max_apl, birthday, swimsuit_id, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING *`,
      [girl.id, girl.name, girl.type, girl.level, girl.pow, girl.tec, girl.stm, girl.apl, girl.maxPow, girl.maxTec, girl.maxStm, girl.maxApl, girl.birthday, girl.swimsuitId]
    );
    return this.mapGirlRow(result.rows[0]);
  }

  async getGirls(): Promise<Girl[]> {
    const result = await this.db.query('SELECT * FROM girls ORDER BY name');
    return result.rows.map(row => this.mapGirlRow(row));
  }

  async getGirlById(id: string): Promise<Girl | undefined> {
    const result = await this.db.query('SELECT * FROM girls WHERE id = $1', [id]);
    return result.rows[0] ? this.mapGirlRow(result.rows[0]) : undefined;
  }

  async updateGirl(id: string, updates: Partial<NewGirl>): Promise<Girl> {
    const setClause = [];
    const values = [];
    let paramIndex = 1;

    if (updates.name !== undefined) {
      setClause.push(`name = $${paramIndex++}`);
      values.push(updates.name);
    }
    if (updates.type !== undefined) {
      setClause.push(`type = $${paramIndex++}`);
      values.push(updates.type);
    }
    if (updates.level !== undefined) {
      setClause.push(`level = $${paramIndex++}`);
      values.push(updates.level);
    }
    if (updates.pow !== undefined) {
      setClause.push(`pow = $${paramIndex++}`);
      values.push(updates.pow);
    }
    if (updates.tec !== undefined) {
      setClause.push(`tec = $${paramIndex++}`);
      values.push(updates.tec);
    }
    if (updates.stm !== undefined) {
      setClause.push(`stm = $${paramIndex++}`);
      values.push(updates.stm);
    }
    if (updates.apl !== undefined) {
      setClause.push(`apl = $${paramIndex++}`);
      values.push(updates.apl);
    }
    if (updates.maxPow !== undefined) {
      setClause.push(`max_pow = $${paramIndex++}`);
      values.push(updates.maxPow);
    }
    if (updates.maxTec !== undefined) {
      setClause.push(`max_tec = $${paramIndex++}`);
      values.push(updates.maxTec);
    }
    if (updates.maxStm !== undefined) {
      setClause.push(`max_stm = $${paramIndex++}`);
      values.push(updates.maxStm);
    }
    if (updates.maxApl !== undefined) {
      setClause.push(`max_apl = $${paramIndex++}`);
      values.push(updates.maxApl);
    }
    if (updates.birthday !== undefined) {
      setClause.push(`birthday = $${paramIndex++}`);
      values.push(updates.birthday);
    }
    if (updates.swimsuitId !== undefined) {
      setClause.push(`swimsuit_id = $${paramIndex++}`);
      values.push(updates.swimsuitId);
    }

    setClause.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const result = await this.db.query(
      `UPDATE girls SET ${setClause.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
      values
    );
    return this.mapGirlRow(result.rows[0]);
  }

  async deleteGirl(id: string): Promise<void> {
    await this.db.query('DELETE FROM girls WHERE id = $1', [id]);
  }

  // Accessories CRUD
  async createAccessory(accessory: NewAccessory): Promise<Accessory> {
    const result = await this.db.query(
      `INSERT INTO accessories (id, name, type, skill_id, pow, tec, stm, apl, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING *`,
      [accessory.id, accessory.name, accessory.type, accessory.skillId, accessory.pow, accessory.tec, accessory.stm, accessory.apl]
    );
    return this.mapAccessoryRow(result.rows[0]);
  }

  async getAccessories(): Promise<Accessory[]> {
    const result = await this.db.query('SELECT * FROM accessories ORDER BY name');
    return result.rows.map(row => this.mapAccessoryRow(row));
  }

  async getAccessoryById(id: string): Promise<Accessory | undefined> {
    const result = await this.db.query('SELECT * FROM accessories WHERE id = $1', [id]);
    return result.rows[0] ? this.mapAccessoryRow(result.rows[0]) : undefined;
  }

  async updateAccessory(id: string, updates: Partial<NewAccessory>): Promise<Accessory> {
    const setClause = [];
    const values = [];
    let paramIndex = 1;

    if (updates.name !== undefined) {
      setClause.push(`name = $${paramIndex++}`);
      values.push(updates.name);
    }
    if (updates.type !== undefined) {
      setClause.push(`type = $${paramIndex++}`);
      values.push(updates.type);
    }
    if (updates.skillId !== undefined) {
      setClause.push(`skill_id = $${paramIndex++}`);
      values.push(updates.skillId);
    }
    if (updates.pow !== undefined) {
      setClause.push(`pow = $${paramIndex++}`);
      values.push(updates.pow);
    }
    if (updates.tec !== undefined) {
      setClause.push(`tec = $${paramIndex++}`);
      values.push(updates.tec);
    }
    if (updates.stm !== undefined) {
      setClause.push(`stm = $${paramIndex++}`);
      values.push(updates.stm);
    }
    if (updates.apl !== undefined) {
      setClause.push(`apl = $${paramIndex++}`);
      values.push(updates.apl);
    }

    setClause.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const result = await this.db.query(
      `UPDATE accessories SET ${setClause.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
      values
    );
    return this.mapAccessoryRow(result.rows[0]);
  }

  async deleteAccessory(id: string): Promise<void> {
    await this.db.query('DELETE FROM accessories WHERE id = $1', [id]);
  }

  // Venus Boards CRUD
  async createVenusBoard(venusBoard: NewVenusBoard): Promise<VenusBoard> {
    const result = await this.db.query(
      `INSERT INTO venus_boards (girl_id, pow, tec, stm, apl, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING *`,
      [venusBoard.girlId, venusBoard.pow, venusBoard.tec, venusBoard.stm, venusBoard.apl]
    );
    return this.mapVenusBoardRow(result.rows[0]);
  }

  async getVenusBoardsByGirlId(girlId: string): Promise<VenusBoard[]> {
    const result = await this.db.query('SELECT * FROM venus_boards WHERE girl_id = $1', [girlId]);
    return result.rows.map(row => this.mapVenusBoardRow(row));
  }

  async updateVenusBoard(id: number, updates: Partial<NewVenusBoard>): Promise<VenusBoard> {
    const setClause = [];
    const values = [];
    let paramIndex = 1;

    if (updates.girlId !== undefined) {
      setClause.push(`girl_id = $${paramIndex++}`);
      values.push(updates.girlId);
    }
    if (updates.pow !== undefined) {
      setClause.push(`pow = $${paramIndex++}`);
      values.push(updates.pow);
    }
    if (updates.tec !== undefined) {
      setClause.push(`tec = $${paramIndex++}`);
      values.push(updates.tec);
    }
    if (updates.stm !== undefined) {
      setClause.push(`stm = $${paramIndex++}`);
      values.push(updates.stm);
    }
    if (updates.apl !== undefined) {
      setClause.push(`apl = $${paramIndex++}`);
      values.push(updates.apl);
    }

    setClause.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const result = await this.db.query(
      `UPDATE venus_boards SET ${setClause.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
      values
    );
    return this.mapVenusBoardRow(result.rows[0]);
  }

  async deleteVenusBoard(id: number): Promise<void> {
    await this.db.query('DELETE FROM venus_boards WHERE id = $1', [id]);
  }

  // User Settings
  async getSetting(key: string): Promise<string | null> {
    const result = await this.db.query('SELECT value FROM user_settings WHERE key = $1', [key]);
    return result.rows[0]?.value || null;
  }

  async setSetting(key: string, value: string): Promise<void> {
    await this.db.query(
      `INSERT INTO user_settings (key, value, created_at, updated_at)
       VALUES ($1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       ON CONFLICT (key) DO UPDATE SET
       value = EXCLUDED.value,
       updated_at = CURRENT_TIMESTAMP`,
      [key, value]
    );
  }

  async deleteSetting(key: string): Promise<void> {
    await this.db.query('DELETE FROM user_settings WHERE key = $1', [key]);
  }

  async createUserSetting(key: string, value: string): Promise<void> {
    await this.setSetting(key, value);
  }

  // Swimsuit Skills junction operations
  async addSkillToSwimsuit(swimsuitId: string, skillId: string, position: number): Promise<void> {
    await this.db.query(
      'INSERT INTO swimsuit_skills (swimsuit_id, skill_id, position) VALUES ($1, $2, $3)',
      [swimsuitId, skillId, position]
    );
  }

  async removeSkillFromSwimsuit(swimsuitId: string, skillId: string): Promise<void> {
    await this.db.query(
      'DELETE FROM swimsuit_skills WHERE swimsuit_id = $1 AND skill_id = $2',
      [swimsuitId, skillId]
    );
  }

  // Girl Accessories junction operations
  async equipAccessoryToGirl(girlId: string, accessoryId: string): Promise<void> {
    await this.db.query(
      'INSERT INTO girl_accessories (girl_id, accessory_id, equipped_at) VALUES ($1, $2, CURRENT_TIMESTAMP)',
      [girlId, accessoryId]
    );
  }

  async addAccessoryToGirl(girlId: string, accessoryId: string): Promise<void> {
    await this.equipAccessoryToGirl(girlId, accessoryId);
  }

  async unequipAccessoryFromGirl(girlId: string, accessoryId: string): Promise<void> {
    await this.db.query(
      'DELETE FROM girl_accessories WHERE girl_id = $1 AND accessory_id = $2',
      [girlId, accessoryId]
    );
  }

  // Complex queries with relations
  async getSwimsuitWithSkills(swimsuitId: string): Promise<SwimsuitWithSkills | null> {
    // Get swimsuit with character
    const swimsuitResult = await this.db.query(`
      SELECT s.*, c.name as character_name
      FROM swimsuits s
      LEFT JOIN characters c ON s.character_id = c.id
      WHERE s.id = $1
    `, [swimsuitId]);

    if (swimsuitResult.rows.length === 0) return null;

    // Get skills for this swimsuit
    const skillsResult = await this.db.query(`
      SELECT sk.*, ss.position
      FROM swimsuit_skills ss
      LEFT JOIN skills sk ON ss.skill_id = sk.id
      WHERE ss.swimsuit_id = $1
      ORDER BY ss.position
    `, [swimsuitId]);

    const swimsuit = this.mapSwimsuitRow(swimsuitResult.rows[0]);
    const skills = skillsResult.rows.map(row => this.mapSkillRow(row));

    return {
      ...swimsuit,
      skills
    };
  }

  async getGirlWithDetails(girlId: string): Promise<GirlWithDetails | null> {
    // Get girl with swimsuit and character
    const girlResult = await this.db.query(`
      SELECT g.*, s.name as swimsuit_name, c.name as character_name
      FROM girls g
      LEFT JOIN swimsuits s ON g.swimsuit_id = s.id
      LEFT JOIN characters c ON s.character_id = c.id
      WHERE g.id = $1
    `, [girlId]);

    if (girlResult.rows.length === 0) return null;

    // Get accessories for this girl
    const accessoriesResult = await this.db.query(`
      SELECT a.*, sk.name as skill_name, sk.type as skill_type
      FROM girl_accessories ga
      LEFT JOIN accessories a ON ga.accessory_id = a.id
      LEFT JOIN skills sk ON a.skill_id = sk.id
      WHERE ga.girl_id = $1
    `, [girlId]);

    // Get venus boards for this girl
    const venusBoardsResult = await this.getVenusBoardsByGirlId(girlId);

    const girl = this.mapGirlRow(girlResult.rows[0]);
    const accessories = accessoriesResult.rows.map(row => this.mapAccessoryRow(row));

    return {
      ...girl,
      swimsuit: girl.swimsuitId ? this.mapSwimsuitRow(girlResult.rows[0]) : undefined,
      accessories,
      venusBoard: venusBoardsResult[0]
    };
  }

  // Helper methods to map database rows to TypeScript objects
  private mapCharacterRow(row: any): Character {
    return {
      id: row.id,
      name: row.name,
      nameJp: row.name_jp,
      nameEn: row.name_en,
      nameZh: row.name_zh,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  private mapSkillRow(row: any): Skill {
    return {
      id: row.id,
      name: row.name,
      type: row.type,
      description: row.description,
      icon: row.icon,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  private mapSwimsuitRow(row: any): Swimsuit {
    return {
      id: row.id,
      name: row.name,
      characterId: row.character_id,
      rarity: row.rarity,
      pow: row.pow,
      tec: row.tec,
      stm: row.stm,
      apl: row.apl,
      releaseDate: new Date(row.release_date),
      reappearDate: row.reappear_date ? new Date(row.reappear_date) : undefined,
      image: row.image,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  private mapGirlRow(row: any): Girl {
    return {
      id: row.id,
      name: row.name,
      type: row.type,
      level: row.level,
      pow: row.pow,
      tec: row.tec,
      stm: row.stm,
      apl: row.apl,
      maxPow: row.max_pow,
      maxTec: row.max_tec,
      maxStm: row.max_stm,
      maxApl: row.max_apl,
      birthday: new Date(row.birthday),
      swimsuitId: row.swimsuit_id,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  private mapAccessoryRow(row: any): Accessory {
    return {
      id: row.id,
      name: row.name,
      type: row.type,
      skillId: row.skill_id,
      pow: row.pow,
      tec: row.tec,
      stm: row.stm,
      apl: row.apl,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  private mapVenusBoardRow(row: any): VenusBoard {
    return {
      id: row.id,
      girlId: row.girl_id,
      pow: row.pow,
      tec: row.tec,
      stm: row.stm,
      apl: row.apl,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  private mapUserSettingRow(row: any): UserSetting {
    return {
      key: row.key,
      value: row.value,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }
}

// Export singleton instance
export const postgresService = new PostgreSQLService();