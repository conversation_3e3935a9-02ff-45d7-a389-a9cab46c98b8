#!/usr/bin/env bun
// PostgreSQL database seeder

import { postgresDb, postgresService } from './index';

// Sample data for seeding
const sampleCharacters = [
  { id: 'kasumi', name: '<PERSON><PERSON><PERSON>', nameJp: 'かすみ', nameEn: '<PERSON><PERSON><PERSON>' },
  { id: 'ayane', name: '<PERSON><PERSON><PERSON>', nameJp: 'あやね', nameEn: '<PERSON>yan<PERSON>' },
  { id: 'hitomi', name: '<PERSON><PERSON>', nameJp: 'ひと<PERSON>', nameEn: '<PERSON><PERSON>' },
  { id: 'he<PERSON>', name: '<PERSON>', nameJp: 'エレナ', nameEn: '<PERSON>' },
  { id: 'marie_rose', name: '<PERSON>', nameJp: 'マリー・ローズ', nameEn: '<PERSON>' },
];

const sampleSkills = [
  { id: 'power_boost', name: '<PERSON> Boost', type: 'offensive', description: 'Increases power stats' },
  { id: 'tech_boost', name: '<PERSON> Boost', type: 'offensive', description: 'Increases technique stats' },
  { id: 'stamina_boost', name: '<PERSON><PERSON><PERSON>', type: 'defensive', description: 'Increases stamina stats' },
  { id: 'appeal_boost', name: '<PERSON> Boost', type: 'support', description: 'Increases appeal stats' },
  { id: 'critical_hit', name: 'Critical Hit', type: 'offensive', description: 'Chance for critical damage' },
];

const sampleSwimsuits = [
  {
    id: 'kasumi_ssr_1',
    name: 'Venus Kasumi',
    characterId: 'kasumi',
    rarity: 'SSR' as const,
    pow: 1000,
    tec: 800,
    stm: 900,
    apl: 850,
    releaseDate: new Date('2024-01-01'),
  },
  {
    id: 'ayane_sr_1',
    name: 'Ninja Ayane',
    characterId: 'ayane',
    rarity: 'SR' as const,
    pow: 800,
    tec: 900,
    stm: 700,
    apl: 750,
    releaseDate: new Date('2024-01-15'),
  },
  {
    id: 'hitomi_r_1',
    name: 'Casual Hitomi',
    characterId: 'hitomi',
    rarity: 'R' as const,
    pow: 600,
    tec: 650,
    stm: 700,
    apl: 600,
    releaseDate: new Date('2024-02-01'),
  },
];

const sampleAccessories = [
  {
    id: 'power_ring',
    name: 'Power Ring',
    type: 'hand' as const,
    skillId: 'power_boost',
    pow: 50,
    tec: 0,
    stm: 0,
    apl: 0,
  },
  {
    id: 'tech_glasses',
    name: 'Tech Glasses',
    type: 'face' as const,
    skillId: 'tech_boost',
    pow: 0,
    tec: 50,
    stm: 0,
    apl: 0,
  },
];

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');

    // Initialize database
    await postgresDb.initialize();

    // Seed characters
    console.log('📝 Seeding characters...');
    for (const character of sampleCharacters) {
      try {
        await postgresService.createCharacter(character);
        console.log(`  ✅ Created character: ${character.name}`);
      } catch (error: any) {
        if (error.message?.includes('duplicate key')) {
          console.log(`  ⚠️  Character already exists: ${character.name}`);
        } else {
          console.error(`  ❌ Failed to create character ${character.name}:`, error);
        }
      }
    }

    // Seed skills
    console.log('🎯 Seeding skills...');
    for (const skill of sampleSkills) {
      try {
        await postgresService.createSkill(skill);
        console.log(`  ✅ Created skill: ${skill.name}`);
      } catch (error: any) {
        if (error.message?.includes('duplicate key')) {
          console.log(`  ⚠️  Skill already exists: ${skill.name}`);
        } else {
          console.error(`  ❌ Failed to create skill ${skill.name}:`, error);
        }
      }
    }

    // Seed swimsuits
    console.log('👙 Seeding swimsuits...');
    for (const swimsuit of sampleSwimsuits) {
      try {
        await postgresService.createSwimsuit(swimsuit);
        console.log(`  ✅ Created swimsuit: ${swimsuit.name}`);
      } catch (error: any) {
        if (error.message?.includes('duplicate key')) {
          console.log(`  ⚠️  Swimsuit already exists: ${swimsuit.name}`);
        } else {
          console.error(`  ❌ Failed to create swimsuit ${swimsuit.name}:`, error);
        }
      }
    }

    // Seed accessories
    console.log('💍 Seeding accessories...');
    for (const accessory of sampleAccessories) {
      try {
        await postgresService.createAccessory(accessory);
        console.log(`  ✅ Created accessory: ${accessory.name}`);
      } catch (error: any) {
        if (error.message?.includes('duplicate key')) {
          console.log(`  ⚠️  Accessory already exists: ${accessory.name}`);
        } else {
          console.error(`  ❌ Failed to create accessory ${accessory.name}:`, error);
        }
      }
    }

    // Add skills to swimsuits
    console.log('🔗 Linking skills to swimsuits...');
    try {
      await postgresService.addSkillToSwimsuit('kasumi_ssr_1', 'power_boost', 0);
      await postgresService.addSkillToSwimsuit('kasumi_ssr_1', 'critical_hit', 1);
      await postgresService.addSkillToSwimsuit('ayane_sr_1', 'tech_boost', 0);
      await postgresService.addSkillToSwimsuit('hitomi_r_1', 'stamina_boost', 0);
      console.log('  ✅ Skills linked to swimsuits');
    } catch (error: any) {
      if (error.message?.includes('duplicate key')) {
        console.log('  ⚠️  Skill-swimsuit links already exist');
      } else {
        console.error('  ❌ Failed to link skills to swimsuits:', error);
      }
    }

    // Set default user settings
    console.log('⚙️  Setting default user settings...');
    await postgresService.setSetting('theme', 'dark');
    await postgresService.setSetting('language', 'en');
    await postgresService.setSetting('sidebarCollapsed', 'false');
    console.log('  ✅ Default settings configured');

    console.log('🎉 Database seeding completed successfully!');

    // Close database connection
    await postgresDb.close();

  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
}

async function clearDatabase() {
  try {
    console.log('🧹 Clearing database...');

    // Initialize database
    await postgresDb.initialize();

    // Clear all tables in reverse order to respect foreign keys
    const tables = [
      'girl_accessories',
      'swimsuit_skills',
      'venus_boards',
      'user_settings',
      'girls',
      'accessories',
      'swimsuits',
      'skills',
      'characters'
    ];

    for (const table of tables) {
      await postgresDb.query(`DELETE FROM ${table}`);
      console.log(`  ✅ Cleared table: ${table}`);
    }

    console.log('🎉 Database cleared successfully!');

    // Close database connection
    await postgresDb.close();

  } catch (error) {
    console.error('❌ Database clearing failed:', error);
    process.exit(1);
  }
}

async function resetDatabase() {
  console.log('🔄 Resetting database...');
  await clearDatabase();
  await seedDatabase();
}

// Handle command line arguments
const command = process.argv[2];

if (import.meta.url === `file://${process.argv[1]}`) {
  switch (command) {
    case 'seed':
      seedDatabase();
      break;
    case 'clear':
      clearDatabase();
      break;
    case 'reset':
      resetDatabase();
      break;
    default:
      console.log('Usage: bun run seeder.ts [seed|clear|reset]');
      console.log('  seed  - Add sample data to the database');
      console.log('  clear - Remove all data from the database');
      console.log('  reset - Clear and then seed the database');
      process.exit(1);
  }
}

export { seedDatabase, clearDatabase, resetDatabase };