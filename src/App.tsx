import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import { useEffect } from 'react';
import { useAppStore } from './store';
import { useTheme } from './lib/useTheme';
import { usePostgreSQLDatabase as useDatabase } from './lib/db';

// Layout Components - Optimized with performance improvements
import { Header } from './components/Header';
import { Sidebar } from './components/Sidebar';
import { AccessibilityProvider, SkipLink } from './components/AccessibilityProvider';

// Main Pages - Lazy loaded for better performance
const HomePage = React.lazy(() => import('./pages/HomePage'));
const SwimsuitPage = React.lazy(() => import('./pages/SwimsuitPage'));
const GirlListPage = React.lazy(() => import('./pages/GirlListPage'));
const ParameterCalculatorPage = React.lazy(() => import('./pages/ParameterCalculatorPage'));
const ExpCalculatorPage = React.lazy(() => import('./pages/ExpCalculatorPage'));
const SettingsPage = React.lazy(() => import('./pages/SettingsPage'));
const SkillsPage = React.lazy(() => import('./pages/SkillsPage'));
const PassiveSkillPage = React.lazy(() => import('./pages/PassiveSkillPage'));
const DecorateBromidePage = React.lazy(() => import('./pages/DecorateBromidePage'));
const AccessoryPage = React.lazy(() => import('./pages/AccessoryPage'));
const LevelPage = React.lazy(() => import('./pages/LevelPage'));
const OwnerRoomPage = React.lazy(() => import('./pages/OwnerRoomPage'));
const MonthlyBattlePage = React.lazy(() => import('./pages/MonthlyBattlePage'));
const MessagesPage = React.lazy(() => import('./pages/MessagesPage'));

// Loading component with optimized styling
const LoadingFallback: React.FC = () => (
  <div className="viewport-optimized flex items-center justify-center min-h-[60vh]">
    <div className="loading doax-card p-8 text-center">
      <div className="w-12 h-12 rounded-full border-2 border-accent-pink/20 border-t-accent-pink mx-auto mb-4 animate-spin"></div>
      <p className="text-muted-foreground">Loading content...</p>
    </div>
  </div>
);

function App() {
  const { i18n } = useTranslation();
  const { currentLanguage } = useAppStore();
  
  // Use theme hook to handle theme application
  useTheme();
  
  // Initialize database
  useDatabase();

  useEffect(() => {
    i18n.changeLanguage(currentLanguage);
  }, [currentLanguage, i18n]);

  const [sidebarCollapsedState, setSidebarCollapsedState] = React.useState(false);
  const [isMobile, setIsMobile] = React.useState(false);

  // Handle mobile detection and sidebar auto-collapse
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (mobile && !sidebarCollapsedState) {
        setSidebarCollapsedState(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [sidebarCollapsedState]);

  const toggleSidebar = React.useCallback(() => {
    setSidebarCollapsedState(prev => !prev);
  }, []);

  return (
    <AccessibilityProvider>
      <Router
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true
        }}
      >
        <div className="viewport-optimized min-h-screen bg-background font-sans antialiased">
          {/* Skip Links for Accessibility */}
          <SkipLink href="#main-content">Skip to main content</SkipLink>
          <SkipLink href="#sidebar-nav">Skip to navigation</SkipLink>

          {/* Enhanced Background Pattern with reduced opacity for better readability */}
          <div className="fixed inset-0 opacity-[0.015] pointer-events-none">
            <div className="absolute inset-0 bg-gradient-to-br from-accent-pink/8 via-accent-cyan/8 to-accent-purple/8" />
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, rgba(233, 30, 99, 0.08) 0%, transparent 25%),
                               radial-gradient(circle at 75% 75%, rgba(0, 188, 212, 0.08) 0%, transparent 25%)`
            }} />
          </div>

          {/* Mobile Overlay for Sidebar */}
          {isMobile && !sidebarCollapsedState && (
            <div 
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-30 md:hidden"
              onClick={() => setSidebarCollapsedState(true)}
              aria-label="Close sidebar overlay"
            />
          )}

          {/* App Layout Container */}
          <div className="relative min-h-screen">
            {/* Sidebar */}
            <Sidebar 
              isCollapsed={sidebarCollapsedState} 
              onToggle={toggleSidebar}
            />
            
            {/* Main Content Container */}
            <div 
              className={`
                min-h-screen transition-all duration-300 ease-in-out
                ${isMobile 
                  ? 'ml-0' 
                  : sidebarCollapsedState 
                    ? 'ml-16' 
                    : 'ml-60'
                }
                flex flex-col
              `}
            >
              {/* Header */}
              <Header />
              
              {/* Main Content */}
              <main 
                id="main-content"
                className="flex-1 overflow-x-hidden"
              >
                <div className="container-responsive">
                  <Suspense fallback={<LoadingFallback />}>
                    <Routes>
                      <Route path="/" element={<HomePage />} />
                      <Route path="/swimsuit" element={<SwimsuitPage />} />
                      <Route path="/girls" element={<GirlListPage />} />
                      <Route path="/calculator/parameter" element={<ParameterCalculatorPage />} />
                      <Route path="/calculator/exp" element={<ExpCalculatorPage />} />
                      <Route path="/settings" element={<SettingsPage />} />
                      <Route path="/skills" element={<SkillsPage />} />
                      <Route path="/data/passive-skill" element={<PassiveSkillPage />} />
                      <Route path="/data/decorate-bromide" element={<DecorateBromidePage />} />
                      <Route path="/data/accessory" element={<AccessoryPage />} />
                      <Route path="/data/level" element={<LevelPage />} />
                      <Route path="/data/owner-room" element={<OwnerRoomPage />} />
                      <Route path="/data/monthly-battle" element={<MonthlyBattlePage />} />
                      <Route path="/tool/messages" element={<MessagesPage />} />
                    </Routes>
                  </Suspense>
                </div>
              </main>
            </div>
          </div>
        </div>
      </Router>
    </AccessibilityProvider>
  );
}

export default App; 