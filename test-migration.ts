#!/usr/bin/env bun
// Test script to verify PostgreSQL migration is working correctly

import { postgresDb, postgresService } from './src/lib/db';

async function testMigration() {
  console.log('🧪 Testing PostgreSQL Migration...\n');

  try {
    // Test 1: Database Connection
    console.log('1️⃣ Testing database connection...');
    await postgresDb.initialize();
    console.log('✅ Database connection successful\n');

    // Test 2: Health Check
    console.log('2️⃣ Testing database health...');
    const health = await postgresDb.checkHealth();
    if (health.isHealthy) {
      console.log('✅ Database health check passed');
    } else {
      console.log('⚠️ Database health issues:', health.errors);
    }
    console.log('');

    // Test 3: Create Test Data
    console.log('3️⃣ Testing data creation...');
    
    // Create a test character
    const testCharacter = {
      id: 'test_character',
      name: 'Test Character',
      nameJp: 'テストキャラクター',
      nameEn: 'Test Character',
    };
    
    try {
      await postgresService.createCharacter(testCharacter);
      console.log('✅ Character creation successful');
    } catch (error: any) {
      if (error.message?.includes('duplicate key')) {
        console.log('✅ Character already exists (expected)');
      } else {
        throw error;
      }
    }

    // Create a test skill
    const testSkill = {
      id: 'test_skill',
      name: 'Test Skill',
      type: 'test',
      description: 'A test skill for migration verification',
    };
    
    try {
      await postgresService.createSkill(testSkill);
      console.log('✅ Skill creation successful');
    } catch (error: any) {
      if (error.message?.includes('duplicate key')) {
        console.log('✅ Skill already exists (expected)');
      } else {
        throw error;
      }
    }

    // Create a test swimsuit
    const testSwimsuit = {
      id: 'test_swimsuit',
      name: 'Test Swimsuit',
      characterId: 'test_character',
      rarity: 'SSR' as const,
      pow: 1000,
      tec: 800,
      stm: 900,
      apl: 850,
      releaseDate: new Date('2024-01-01'),
    };
    
    try {
      await postgresService.createSwimsuit(testSwimsuit);
      console.log('✅ Swimsuit creation successful');
    } catch (error: any) {
      if (error.message?.includes('duplicate key')) {
        console.log('✅ Swimsuit already exists (expected)');
      } else {
        throw error;
      }
    }
    console.log('');

    // Test 4: Data Retrieval
    console.log('4️⃣ Testing data retrieval...');
    
    const characters = await postgresService.getCharacters();
    console.log(`✅ Retrieved ${characters.length} characters`);
    
    const skills = await postgresService.getSkills();
    console.log(`✅ Retrieved ${skills.length} skills`);
    
    const swimsuits = await postgresService.getSwimsuits();
    console.log(`✅ Retrieved ${swimsuits.length} swimsuits`);
    console.log('');

    // Test 5: User Settings
    console.log('5️⃣ Testing user settings...');
    
    await postgresService.setSetting('test_setting', 'test_value');
    const settingValue = await postgresService.getSetting('test_setting');
    
    if (settingValue === 'test_value') {
      console.log('✅ User settings working correctly');
    } else {
      console.log('❌ User settings test failed');
    }
    console.log('');

    // Test 6: Junction Tables
    console.log('6️⃣ Testing junction tables...');
    
    try {
      await postgresService.addSkillToSwimsuit('test_swimsuit', 'test_skill', 0);
      console.log('✅ Swimsuit-skill junction working');
    } catch (error: any) {
      if (error.message?.includes('duplicate key')) {
        console.log('✅ Swimsuit-skill junction already exists (expected)');
      } else {
        throw error;
      }
    }
    console.log('');

    // Test 7: Complex Queries
    console.log('7️⃣ Testing complex queries...');
    
    const swimsuitWithSkills = await postgresService.getSwimsuitWithSkills('test_swimsuit');
    if (swimsuitWithSkills) {
      console.log(`✅ Complex query successful - swimsuit has ${swimsuitWithSkills.skills.length} skills`);
    } else {
      console.log('⚠️ Complex query returned null (swimsuit not found)');
    }
    console.log('');

    // Test 8: Database Performance
    console.log('8️⃣ Testing database performance...');
    
    const startTime = Date.now();
    await Promise.all([
      postgresService.getCharacters(),
      postgresService.getSkills(),
      postgresService.getSwimsuits(),
    ]);
    const endTime = Date.now();
    
    console.log(`✅ Concurrent queries completed in ${endTime - startTime}ms`);
    console.log('');

    // Final Summary
    console.log('🎉 Migration Test Results:');
    console.log('✅ Database connection: PASSED');
    console.log('✅ Health check: PASSED');
    console.log('✅ Data creation: PASSED');
    console.log('✅ Data retrieval: PASSED');
    console.log('✅ User settings: PASSED');
    console.log('✅ Junction tables: PASSED');
    console.log('✅ Complex queries: PASSED');
    console.log('✅ Performance: PASSED');
    console.log('');
    console.log('🎊 PostgreSQL migration is working correctly!');

  } catch (error) {
    console.error('❌ Migration test failed:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await postgresDb.close();
  }
}

// Run the test
if (import.meta.main) {
  testMigration();
}
